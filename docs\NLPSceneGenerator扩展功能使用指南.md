# NLPSceneGenerator 扩展功能使用指南

## 概述

NLPSceneGenerator 已经进行了全面的扩展开发，现在支持以下高级功能：

1. **自定义风格系统** - 创建和应用自定义的视觉风格
2. **自定义对象类型** - 定义和使用自定义的3D对象
3. **外部AI服务集成** - 集成多种AI服务增强场景生成
4. **智能优化系统** - AI驱动的性能和质量优化
5. **高级语言理解** - 增强的自然语言处理能力

## 功能特性

### 1. 自定义风格系统

#### 创建自定义风格
```typescript
import { NLPSceneGenerator, CustomStyleConfig, MaterialType } from '@dl-engine/ai';

const modernStyle: CustomStyleConfig = {
  name: 'modern_minimalist',
  description: '现代极简主义风格',
  materialPresets: [
    {
      name: 'clean_metal',
      type: MaterialType.PHYSICAL,
      properties: { 
        color: 0xF5F5F5, 
        roughness: 0.1, 
        metalness: 0.9 
      },
      applicableObjects: ['桌子', '椅子', '灯具']
    },
    {
      name: 'soft_fabric',
      type: MaterialType.STANDARD,
      properties: { 
        color: 0xE8E8E8, 
        roughness: 0.8, 
        metalness: 0.0 
      },
      applicableObjects: ['沙发', '窗帘', '地毯']
    }
  ],
  lightingPresets: [{
    name: 'soft_daylight',
    ambientIntensity: 0.3,
    directionalIntensity: 0.7,
    colorTemperature: 6500,
    shadowSettings: {
      enabled: true,
      quality: 'high',
      softness: 0.6,
      bias: -0.0001
    }
  }],
  objectModifiers: [],
  atmosphereSettings: {
    fogDensity: 0.0,
    fogColor: '#ffffff',
    skyboxType: 'gradient',
    postProcessingEffects: ['bloom', 'ssao']
  },
  colorPalette: ['#ffffff', '#f8f8f8', '#e0e0e0', '#c0c0c0', '#a0a0a0']
};

// 注册自定义风格
const nlpGenerator = engine.getSystem('NLPSceneGenerator');
nlpGenerator.registerCustomStyle(modernStyle);
```

#### 使用自定义风格
```typescript
const scene = await nlpGenerator.generateSceneFromNaturalLanguage(
  '创建一个现代化的办公室，有简洁的桌椅和柔和的光照',
  {
    style: 'modern_minimalist', // 使用自定义风格
    quality: 90,
    maxObjects: 30,
    constraints: {
      maxPolygons: 150000,
      targetFrameRate: 60
    },
    customStyle: modernStyle // 传入风格配置
  }
);
```

### 2. 自定义对象类型

#### 定义自定义对象
```typescript
import { CustomObjectType } from '@dl-engine/ai';
import * as THREE from 'three';

const smartDesk: CustomObjectType = {
  name: 'smart_desk',
  category: 'furniture',
  description: '智能办公桌，带有内置显示屏和无线充电区域',
  geometryFactory: (params) => {
    // 创建复合几何体
    const deskGeometry = new THREE.BoxGeometry(1.6, 0.05, 0.8);
    // 可以添加更复杂的几何体组合逻辑
    return deskGeometry;
  },
  defaultMaterial: 'modern_wood',
  boundingBox: new THREE.Box3(
    new THREE.Vector3(-0.8, 0, -0.4),
    new THREE.Vector3(0.8, 0.8, 0.4)
  ),
  tags: ['furniture', 'desk', 'smart', 'office'],
  complexity: 5,
  additionalComponents: ['InteractiveComponent', 'SmartDeviceComponent']
};

// 注册自定义对象类型
nlpGenerator.registerCustomObject(smartDesk);
```

#### 在场景生成中使用
```typescript
const scene = await nlpGenerator.generateSceneFromNaturalLanguage(
  '创建一个智能办公室，包含智能办公桌和现代化设备',
  {
    style: 'modern',
    quality: 85,
    maxObjects: 25,
    constraints: {
      maxPolygons: 120000,
      targetFrameRate: 60
    },
    customObjects: [smartDesk], // 传入自定义对象
    enableAdvancedFeatures: true
  }
);
```

### 3. 外部AI服务集成

#### 配置AI服务
```typescript
import { ExternalAIServiceConfig, AICapability } from '@dl-engine/ai';

const openaiService: ExternalAIServiceConfig = {
  name: 'openai_gpt4',
  endpoint: 'https://api.openai.com/v1',
  apiKey: 'your-api-key-here',
  model: 'gpt-4',
  capabilities: [
    AICapability.TEXT_UNDERSTANDING,
    AICapability.SCENE_PLANNING,
    AICapability.OPTIMIZATION
  ],
  rateLimits: {
    requestsPerMinute: 60,
    requestsPerHour: 1000,
    maxConcurrent: 5
  }
};

const localNLPService: ExternalAIServiceConfig = {
  name: 'local_bert',
  endpoint: 'http://localhost:8080/api',
  model: 'bert-base-chinese',
  capabilities: [AICapability.TEXT_UNDERSTANDING],
  rateLimits: {
    requestsPerMinute: 120,
    requestsPerHour: 5000,
    maxConcurrent: 10
  },
  fallbackService: 'openai_gpt4'
};

// 注册AI服务
nlpGenerator.registerAIService(openaiService);
nlpGenerator.registerAIService(localNLPService);
```

#### 使用AI增强功能
```typescript
const scene = await nlpGenerator.generateSceneFromNaturalLanguage(
  '创建一个温馨的家庭客厅，要有舒适的沙发、温暖的灯光，还要体现家庭的温馨氛围',
  {
    style: 'cozy_home',
    quality: 95,
    maxObjects: 40,
    constraints: {
      maxPolygons: 200000,
      targetFrameRate: 60
    },
    aiServices: ['openai_gpt4', 'local_bert'], // 指定使用的AI服务
    enableAdvancedFeatures: true
  }
);
```

### 4. 高级生成选项

#### 使用随机种子确保可重现性
```typescript
const scene = await nlpGenerator.generateSceneFromNaturalLanguage(
  '创建一个科幻实验室场景',
  {
    style: 'scifi',
    quality: 80,
    maxObjects: 35,
    constraints: {
      maxPolygons: 180000,
      targetFrameRate: 60
    },
    seedValue: 12345, // 固定种子值，确保每次生成相同的结果
    enableAdvancedFeatures: true
  }
);
```

#### 使用模板场景
```typescript
const scene = await nlpGenerator.generateSceneFromNaturalLanguage(
  '在现有的办公室基础上添加一些植物和装饰品',
  {
    style: 'modern',
    quality: 75,
    maxObjects: 20,
    constraints: {
      maxPolygons: 100000,
      targetFrameRate: 60
    },
    templateScene: 'office_template_001', // 基于模板场景
    enableAdvancedFeatures: true
  }
);
```

### 5. 性能监控和优化

#### 获取性能指标
```typescript
// 获取生成器的性能指标
const metrics = nlpGenerator.getPerformanceMetrics();
console.log('性能指标:', {
  总生成次数: metrics.totalGenerations,
  平均生成时间: `${metrics.averageGenerationTime}ms`,
  缓存命中率: `${(metrics.cacheHitRate * 100).toFixed(2)}%`,
  错误率: `${(metrics.errorRate * 100).toFixed(2)}%`
});
```

#### 缓存管理
```typescript
// 清除缓存
nlpGenerator.clearCache();

// 设置缓存大小限制
nlpGenerator.setCacheLimit(50);
```

### 6. 事件监听

#### 监听生成事件
```typescript
// 监听场景生成完成事件
nlpGenerator.addEventListener('sceneGenerated', (event) => {
  console.log('场景生成完成:', {
    输入文本: event.userInput,
    处理时间: event.metrics.processingTime,
    场景实体数量: event.scene.getEntities().length,
    理解结果: event.understanding
  });
});

// 监听生成错误事件
nlpGenerator.addEventListener('generationError', (event) => {
  console.error('场景生成失败:', {
    输入文本: event.userInput,
    错误信息: event.error.message,
    选项: event.options
  });
});
```

## 最佳实践

### 1. 风格设计建议
- 为每种风格定义清晰的材质预设
- 使用一致的颜色调色板
- 合理配置光照参数以匹配风格氛围
- 适当使用后处理效果增强视觉效果

### 2. 自定义对象设计
- 保持几何体复杂度适中（complexity 1-10）
- 使用有意义的标签便于匹配
- 合理设置边界框以避免碰撞
- 考虑添加交互组件增强功能

### 3. AI服务配置
- 配置合适的速率限制避免超额
- 设置fallback服务确保可靠性
- 根据需求选择合适的AI能力
- 定期监控API使用情况

### 4. 性能优化
- 合理设置质量参数平衡效果和性能
- 使用缓存减少重复计算
- 监控性能指标及时调整
- 在移动设备上适当降低复杂度

## 故障排除

### 常见问题
1. **AI服务连接失败** - 检查网络连接和API密钥
2. **自定义风格不生效** - 确认风格已正确注册
3. **性能问题** - 降低质量参数或减少对象数量
4. **缓存问题** - 清除缓存或调整缓存大小

### 调试技巧
- 启用详细日志输出
- 使用性能指标监控
- 分步测试各个功能模块
- 检查事件监听器的错误信息
